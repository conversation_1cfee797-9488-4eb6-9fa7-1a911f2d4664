<template>
  <div class="query-editor">
    <div class="editor-header">
      <el-select 
        v-model="selectedConnection" 
        placeholder="选择数据库连接"
        style="width: 300px"
        @change="onConnectionChange"
      >
        <el-option
          v-for="conn in connections"
          :key="conn.id"
          :label="conn.name"
          :value="conn.id"
          :disabled="!conn.isConnected"
        >
          <span>{{ conn.name }}</span>
          <el-tag 
            :type="conn.isConnected ? 'success' : 'danger'" 
            size="small" 
            style="margin-left: 8px"
          >
            {{ conn.isConnected ? '已连接' : '未连接' }}
          </el-tag>
        </el-option>
      </el-select>

      <div class="editor-actions">
        <el-button 
          type="primary" 
          @click="executeQuery"
          :disabled="!selectedConnection || !sqlQuery.trim()"
          :loading="executing"
        >
          <el-icon><CaretRight /></el-icon>
          执行查询
        </el-button>
        <el-button @click="clearEditor">清空</el-button>
        <el-button @click="formatQuery">格式化</el-button>
      </div>
    </div>

    <div class="editor-content">
      <!-- SQL编辑器 -->
      <div class="sql-editor">
        <el-input
          v-model="sqlQuery"
          type="textarea"
          :rows="12"
          placeholder="请输入SQL查询语句..."
          class="sql-textarea"
        />
      </div>

      <!-- 查询结果 -->
      <div class="query-results" v-if="queryResult">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="查询结果" name="result">
            <div class="result-info">
              <span>执行时间: {{ queryResult.executionTime }}ms</span>
              <span v-if="queryResult.rows">
                | 返回 {{ queryResult.rows.length }} 行数据
              </span>
              <span v-if="queryResult.affectedRows !== undefined">
                | 影响 {{ queryResult.affectedRows }} 行
              </span>
            </div>

            <el-table 
              v-if="queryResult.rows && queryResult.rows.length > 0"
              :data="queryResult.rows.slice(0, 100)"
              style="width: 100%; margin-top: 10px"
              max-height="400"
            >
              <el-table-column
                v-for="(column, index) in queryResult.columns"
                :key="index"
                :prop="index.toString()"
                :label="column"
                show-overflow-tooltip
              >
                <template #default="{ row }">
                  {{ row[index] }}
                </template>
              </el-table-column>
            </el-table>

            <el-empty 
              v-else-if="queryResult.rows && queryResult.rows.length === 0"
              description="查询无结果"
            />
          </el-tab-pane>

          <el-tab-pane label="错误信息" name="error" v-if="queryResult.error">
            <el-alert
              :title="queryResult.error"
              type="error"
              show-icon
              :closable="false"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { CaretRight } from '@element-plus/icons-vue'
import { databaseApi } from '@/api/database'
import type { DatabaseConnection, QueryResult } from '@/types'

const connections = ref<DatabaseConnection[]>([])
const selectedConnection = ref<string>('')
const sqlQuery = ref('SELECT * FROM users LIMIT 10;')
const queryResult = ref<QueryResult | null>(null)
const executing = ref(false)
const activeTab = ref('result')

const loadConnections = async () => {
  try {
    connections.value = await databaseApi.getConnections()
    // 自动选择第一个已连接的数据库
    const connectedDb = connections.value.find(conn => conn.isConnected)
    if (connectedDb) {
      selectedConnection.value = connectedDb.id
    }
  } catch (error) {
    ElMessage.error('加载连接列表失败')
    console.error(error)
  }
}

const onConnectionChange = () => {
  queryResult.value = null
}

const executeQuery = async () => {
  if (!selectedConnection.value || !sqlQuery.value.trim()) {
    ElMessage.warning('请选择数据库连接并输入SQL语句')
    return
  }

  executing.value = true
  try {
    queryResult.value = await databaseApi.executeQuery(
      selectedConnection.value,
      sqlQuery.value
    )
    
    if (queryResult.value.error) {
      activeTab.value = 'error'
      ElMessage.error('查询执行失败')
    } else {
      activeTab.value = 'result'
      ElMessage.success('查询执行成功')
    }
  } catch (error) {
    ElMessage.error('查询执行失败')
    console.error(error)
  } finally {
    executing.value = false
  }
}

const clearEditor = () => {
  sqlQuery.value = ''
  queryResult.value = null
}

const formatQuery = () => {
  // 简单的SQL格式化
  sqlQuery.value = sqlQuery.value
    .replace(/\s+/g, ' ')
    .replace(/\s*,\s*/g, ',\n  ')
    .replace(/\s*(SELECT|FROM|WHERE|ORDER BY|GROUP BY|HAVING|LIMIT)\s+/gi, '\n$1 ')
    .trim()
}

onMounted(() => {
  loadConnections()
})
</script>

<style scoped>
.query-editor {
  padding: 20px;
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e6e6e6;
}

.editor-actions {
  display: flex;
  gap: 10px;
}

.editor-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.sql-editor {
  flex: 0 0 auto;
}

.sql-textarea :deep(.el-textarea__inner) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
}

.query-results {
  flex: 1;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  overflow: hidden;
}

.result-info {
  padding: 10px 15px;
  background: #f5f7fa;
  border-bottom: 1px solid #e6e6e6;
  font-size: 14px;
  color: #606266;
}

.result-info span {
  margin-right: 15px;
}
</style>
