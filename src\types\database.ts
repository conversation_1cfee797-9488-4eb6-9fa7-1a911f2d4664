// 数据库连接配置
export interface DatabaseConnection {
  id: string
  name: string
  type: 'mysql' | 'postgresql' | 'sqlite' | 'mongodb' | 'redis'
  host: string
  port: number
  username: string
  password: string
  database: string
  ssl?: boolean
  isConnected?: boolean
  createdAt: Date
  updatedAt: Date
}

// 数据库表结构
export interface DatabaseTable {
  name: string
  schema?: string
  type: 'table' | 'view'
  columns: TableColumn[]
  rowCount?: number
  size?: string
}

// 表字段
export interface TableColumn {
  name: string
  type: string
  nullable: boolean
  defaultValue?: any
  isPrimaryKey: boolean
  isForeignKey: boolean
  comment?: string
}

// 查询结果
export interface QueryResult {
  columns: string[]
  rows: any[]
  affectedRows?: number
  executionTime: number
  error?: string
}

// SQL查询历史
export interface QueryHistory {
  id: string
  sql: string
  database: string
  executedAt: Date
  executionTime: number
  success: boolean
  error?: string
}
