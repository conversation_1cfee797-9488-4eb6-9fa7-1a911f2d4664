import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/dashboard'
    },
    {
      path: '/dashboard',
      name: 'dashboard',
      component: () => import('@/views/DashboardView.vue'),
      meta: { title: '仪表盘' }
    },
    {
      path: '/connections',
      name: 'connections',
      component: () => import('@/views/connection/ConnectionList.vue'),
      meta: { title: '连接管理' }
    },
    {
      path: '/connections/new',
      name: 'connection-new',
      component: () => import('@/views/connection/ConnectionForm.vue'),
      meta: { title: '新建连接' }
    },
    {
      path: '/connections/:id/edit',
      name: 'connection-edit',
      component: () => import('@/views/connection/ConnectionForm.vue'),
      meta: { title: '编辑连接' }
    },
    {
      path: '/database/tables',
      name: 'database-tables',
      component: () => import('@/views/database/TableList.vue'),
      meta: { title: '表管理' }
    },
    {
      path: '/database/structure',
      name: 'database-structure',
      component: () => import('@/views/database/DatabaseStructure.vue'),
      meta: { title: '数据库结构' }
    },
    {
      path: '/query',
      name: 'query',
      component: () => import('@/views/query/QueryEditor.vue'),
      meta: { title: 'SQL查询' }
    },
    {
      path: '/history',
      name: 'history',
      component: () => import('@/views/query/QueryHistory.vue'),
      meta: { title: '查询历史' }
    },
    {
      path: '/settings',
      name: 'settings',
      component: () => import('@/views/SettingsView.vue'),
      meta: { title: '系统设置' }
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      component: () => import('@/views/NotFoundView.vue'),
      meta: { title: '页面未找到' }
    }
  ],
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 数据库管理平台`
  }
  next()
})

export default router
