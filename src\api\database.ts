import type { DatabaseConnection, DatabaseTable, QueryResult, QueryHistory } from '@/types'

// 模拟API基础URL
const API_BASE_URL = '/api'

// 数据库连接相关API
export const databaseApi = {
  // 获取所有连接
  async getConnections(): Promise<DatabaseConnection[]> {
    // 模拟数据，实际项目中应该调用真实API
    return [
      {
        id: '1',
        name: 'MySQL Production',
        type: 'mysql',
        host: 'localhost',
        port: 3306,
        username: 'root',
        password: '',
        database: 'production_db',
        isConnected: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: '2',
        name: 'PostgreSQL Dev',
        type: 'postgresql',
        host: 'localhost',
        port: 5432,
        username: 'postgres',
        password: '',
        database: 'dev_db',
        isConnected: false,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]
  },

  // 创建连接
  async createConnection(connection: Omit<DatabaseConnection, 'id' | 'createdAt' | 'updatedAt'>): Promise<DatabaseConnection> {
    // 模拟创建连接
    return {
      ...connection,
      id: Date.now().toString(),
      createdAt: new Date(),
      updatedAt: new Date()
    }
  },

  // 测试连接
  async testConnection(connection: Partial<DatabaseConnection>): Promise<boolean> {
    // 模拟连接测试
    await new Promise(resolve => setTimeout(resolve, 1000))
    return Math.random() > 0.3 // 70% 成功率
  },

  // 获取数据库表列表
  async getTables(connectionId: string): Promise<DatabaseTable[]> {
    // 模拟表数据
    return [
      {
        name: 'users',
        type: 'table',
        columns: [
          { name: 'id', type: 'int', nullable: false, isPrimaryKey: true, isForeignKey: false },
          { name: 'username', type: 'varchar(50)', nullable: false, isPrimaryKey: false, isForeignKey: false },
          { name: 'email', type: 'varchar(100)', nullable: false, isPrimaryKey: false, isForeignKey: false },
          { name: 'created_at', type: 'timestamp', nullable: false, isPrimaryKey: false, isForeignKey: false }
        ],
        rowCount: 1250
      },
      {
        name: 'orders',
        type: 'table',
        columns: [
          { name: 'id', type: 'int', nullable: false, isPrimaryKey: true, isForeignKey: false },
          { name: 'user_id', type: 'int', nullable: false, isPrimaryKey: false, isForeignKey: true },
          { name: 'total', type: 'decimal(10,2)', nullable: false, isPrimaryKey: false, isForeignKey: false },
          { name: 'status', type: 'varchar(20)', nullable: false, isPrimaryKey: false, isForeignKey: false }
        ],
        rowCount: 3420
      }
    ]
  },

  // 执行SQL查询
  async executeQuery(connectionId: string, sql: string): Promise<QueryResult> {
    // 模拟查询执行
    await new Promise(resolve => setTimeout(resolve, 500))
    
    return {
      columns: ['id', 'username', 'email', 'created_at'],
      rows: [
        [1, 'admin', '<EMAIL>', '2024-01-01 10:00:00'],
        [2, 'user1', '<EMAIL>', '2024-01-02 11:00:00'],
        [3, 'user2', '<EMAIL>', '2024-01-03 12:00:00']
      ],
      executionTime: 0.5
    }
  },

  // 获取查询历史
  async getQueryHistory(connectionId: string): Promise<QueryHistory[]> {
    return [
      {
        id: '1',
        sql: 'SELECT * FROM users LIMIT 10',
        database: 'production_db',
        executedAt: new Date(Date.now() - 3600000),
        executionTime: 0.3,
        success: true
      },
      {
        id: '2',
        sql: 'SELECT COUNT(*) FROM orders',
        database: 'production_db',
        executedAt: new Date(Date.now() - 7200000),
        executionTime: 0.1,
        success: true
      }
    ]
  }
}
