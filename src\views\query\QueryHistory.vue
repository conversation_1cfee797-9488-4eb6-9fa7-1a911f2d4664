<template>
  <div class="query-history">
    <div class="page-header">
      <h2>查询历史</h2>
    </div>
    <el-card>
      <el-table :data="history" v-loading="loading" style="width: 100%">
        <el-table-column prop="sql" label="SQL语句" show-overflow-tooltip />
        <el-table-column prop="database" label="数据库" width="150" />
        <el-table-column prop="executedAt" label="执行时间" width="180">
          <template #default="{ row }">
            {{ new Date(row.executedAt).toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="executionTime" label="耗时" width="100">
          <template #default="{ row }">
            {{ row.executionTime }}ms
          </template>
        </el-table-column>
        <el-table-column prop="success" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.success ? 'success' : 'danger'" size="small">
              {{ row.success ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button size="small" @click="rerunQuery(row)">
              重新执行
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { databaseApi } from '@/api/database'
import type { QueryHistory } from '@/types'

const router = useRouter()
const loading = ref(false)
const history = ref<QueryHistory[]>([])

const loadHistory = async () => {
  loading.value = true
  try {
    // 这里应该加载所有连接的查询历史
    const connections = await databaseApi.getConnections()
    if (connections.length > 0) {
      history.value = await databaseApi.getQueryHistory(connections[0].id)
    }
  } catch (error) {
    ElMessage.error('加载查询历史失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const rerunQuery = (query: QueryHistory) => {
  router.push({
    path: '/query',
    query: { sql: query.sql }
  })
}

onMounted(() => {
  loadHistory()
})
</script>

<style scoped>
.query-history {
  padding: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
  margin-bottom: 20px;
}
</style>
