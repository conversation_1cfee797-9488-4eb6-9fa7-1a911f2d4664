<template>
  <div class="dashboard">
    <el-row :gutter="20">
      <!-- 统计卡片 -->
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon color="#409EFF"><Connection /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.connections }}</div>
              <div class="stat-label">数据库连接</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon color="#67C23A"><Database /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.tables }}</div>
              <div class="stat-label">数据表</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon color="#E6A23C"><Search /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.queries }}</div>
              <div class="stat-label">今日查询</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon color="#F56C6C"><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.avgTime }}ms</div>
              <div class="stat-label">平均响应时间</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 最近连接 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最近连接</span>
              <el-button type="primary" size="small" @click="$router.push('/connections')">
                查看全部
              </el-button>
            </div>
          </template>
          <el-table :data="recentConnections" style="width: 100%">
            <el-table-column prop="name" label="连接名称" />
            <el-table-column prop="type" label="类型" width="80" />
            <el-table-column prop="status" label="状态" width="80">
              <template #default="{ row }">
                <el-tag :type="row.isConnected ? 'success' : 'danger'" size="small">
                  {{ row.isConnected ? '已连接' : '未连接' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>

      <!-- 查询历史 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最近查询</span>
              <el-button type="primary" size="small" @click="$router.push('/history')">
                查看全部
              </el-button>
            </div>
          </template>
          <el-table :data="recentQueries" style="width: 100%">
            <el-table-column prop="sql" label="SQL语句" show-overflow-tooltip />
            <el-table-column prop="executionTime" label="执行时间" width="100">
              <template #default="{ row }">
                {{ row.executionTime }}ms
              </template>
            </el-table-column>
            <el-table-column prop="success" label="状态" width="80">
              <template #default="{ row }">
                <el-tag :type="row.success ? 'success' : 'danger'" size="small">
                  {{ row.success ? '成功' : '失败' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Connection, Database, Search, Clock } from '@element-plus/icons-vue'
import { databaseApi } from '@/api/database'
import type { DatabaseConnection, QueryHistory } from '@/types'

const stats = ref({
  connections: 0,
  tables: 0,
  queries: 0,
  avgTime: 0
})

const recentConnections = ref<DatabaseConnection[]>([])
const recentQueries = ref<QueryHistory[]>([])

const loadDashboardData = async () => {
  try {
    // 加载连接数据
    const connections = await databaseApi.getConnections()
    recentConnections.value = connections.slice(0, 5)
    stats.value.connections = connections.length

    // 加载查询历史
    if (connections.length > 0) {
      const history = await databaseApi.getQueryHistory(connections[0].id)
      recentQueries.value = history.slice(0, 5)
      stats.value.queries = history.length
      
      // 计算平均响应时间
      const totalTime = history.reduce((sum, query) => sum + query.executionTime, 0)
      stats.value.avgTime = Math.round(totalTime / history.length * 1000) || 0
    }

    // 模拟表数量
    stats.value.tables = 25
  } catch (error) {
    console.error('加载仪表盘数据失败:', error)
  }
}

onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.stat-card {
  margin-bottom: 20px;
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  font-size: 32px;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
