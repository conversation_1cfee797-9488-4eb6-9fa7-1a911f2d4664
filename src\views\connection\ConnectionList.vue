<template>
  <div class="connection-list">
    <div class="page-header">
      <h2>数据库连接管理</h2>
      <el-button type="primary" @click="$router.push('/connections/new')">
        <el-icon><Plus /></el-icon>
        新建连接
      </el-button>
    </div>

    <el-card>
      <el-table :data="connections" v-loading="loading" style="width: 100%">
        <el-table-column prop="name" label="连接名称" />
        <el-table-column prop="type" label="数据库类型" width="120">
          <template #default="{ row }">
            <el-tag>{{ row.type.toUpperCase() }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="host" label="主机地址" />
        <el-table-column prop="port" label="端口" width="80" />
        <el-table-column prop="database" label="数据库名" />
        <el-table-column prop="isConnected" label="连接状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.isConnected ? 'success' : 'danger'" size="small">
              {{ row.isConnected ? '已连接' : '未连接' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button 
              size="small" 
              :type="row.isConnected ? 'warning' : 'success'"
              @click="toggleConnection(row)"
              :loading="row.connecting"
            >
              {{ row.isConnected ? '断开' : '连接' }}
            </el-button>
            <el-button size="small" @click="editConnection(row)">
              编辑
            </el-button>
            <el-popconfirm
              title="确定要删除这个连接吗？"
              @confirm="deleteConnection(row)"
            >
              <template #reference>
                <el-button size="small" type="danger">删除</el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { databaseApi } from '@/api/database'
import type { DatabaseConnection } from '@/types'

const router = useRouter()
const loading = ref(false)
const connections = ref<DatabaseConnection[]>([])

const loadConnections = async () => {
  loading.value = true
  try {
    connections.value = await databaseApi.getConnections()
  } catch (error) {
    ElMessage.error('加载连接列表失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const toggleConnection = async (connection: DatabaseConnection) => {
  connection.connecting = true
  try {
    if (connection.isConnected) {
      // 断开连接
      connection.isConnected = false
      ElMessage.success('连接已断开')
    } else {
      // 测试连接
      const success = await databaseApi.testConnection(connection)
      if (success) {
        connection.isConnected = true
        ElMessage.success('连接成功')
      } else {
        ElMessage.error('连接失败，请检查配置')
      }
    }
  } catch (error) {
    ElMessage.error('操作失败')
    console.error(error)
  } finally {
    connection.connecting = false
  }
}

const editConnection = (connection: DatabaseConnection) => {
  router.push(`/connections/${connection.id}/edit`)
}

const deleteConnection = async (connection: DatabaseConnection) => {
  try {
    // 这里应该调用删除API
    const index = connections.value.findIndex(c => c.id === connection.id)
    if (index > -1) {
      connections.value.splice(index, 1)
      ElMessage.success('连接已删除')
    }
  } catch (error) {
    ElMessage.error('删除失败')
    console.error(error)
  }
}

onMounted(() => {
  loadConnections()
})
</script>

<style scoped>
.connection-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}
</style>
