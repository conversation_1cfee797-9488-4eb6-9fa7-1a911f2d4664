<template>
  <div class="table-list">
    <div class="page-header">
      <h2>数据表管理</h2>
      <el-select 
        v-model="selectedConnection" 
        placeholder="选择数据库连接"
        style="width: 300px"
        @change="loadTables"
      >
        <el-option
          v-for="conn in connections"
          :key="conn.id"
          :label="conn.name"
          :value="conn.id"
          :disabled="!conn.isConnected"
        />
      </el-select>
    </div>

    <el-card v-loading="loading">
      <el-table :data="tables" style="width: 100%">
        <el-table-column prop="name" label="表名" />
        <el-table-column prop="type" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="row.type === 'table' ? 'primary' : 'info'">
              {{ row.type === 'table' ? '表' : '视图' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="rowCount" label="行数" width="120">
          <template #default="{ row }">
            {{ row.rowCount?.toLocaleString() || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="size" label="大小" width="120" />
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button size="small" @click="viewStructure(row)">
              查看结构
            </el-button>
            <el-button size="small" @click="browseData(row)">
              浏览数据
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-empty v-if="!loading && tables.length === 0" description="请选择数据库连接" />
    </el-card>

    <!-- 表结构对话框 -->
    <el-dialog
      v-model="structureDialogVisible"
      :title="`表结构 - ${selectedTable?.name}`"
      width="80%"
    >
      <el-table :data="selectedTable?.columns" style="width: 100%">
        <el-table-column prop="name" label="字段名" />
        <el-table-column prop="type" label="类型" />
        <el-table-column prop="nullable" label="允许空值" width="100">
          <template #default="{ row }">
            <el-tag :type="row.nullable ? 'warning' : 'success'" size="small">
              {{ row.nullable ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="defaultValue" label="默认值" />
        <el-table-column prop="isPrimaryKey" label="主键" width="80">
          <template #default="{ row }">
            <el-icon v-if="row.isPrimaryKey" color="#409EFF"><Key /></el-icon>
          </template>
        </el-table-column>
        <el-table-column prop="comment" label="备注" show-overflow-tooltip />
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Key } from '@element-plus/icons-vue'
import { databaseApi } from '@/api/database'
import type { DatabaseConnection, DatabaseTable } from '@/types'

const router = useRouter()
const loading = ref(false)
const connections = ref<DatabaseConnection[]>([])
const selectedConnection = ref<string>('')
const tables = ref<DatabaseTable[]>([])
const structureDialogVisible = ref(false)
const selectedTable = ref<DatabaseTable | null>(null)

const loadConnections = async () => {
  try {
    connections.value = await databaseApi.getConnections()
    const connectedDb = connections.value.find(conn => conn.isConnected)
    if (connectedDb) {
      selectedConnection.value = connectedDb.id
      await loadTables()
    }
  } catch (error) {
    ElMessage.error('加载连接列表失败')
    console.error(error)
  }
}

const loadTables = async () => {
  if (!selectedConnection.value) {
    tables.value = []
    return
  }

  loading.value = true
  try {
    tables.value = await databaseApi.getTables(selectedConnection.value)
  } catch (error) {
    ElMessage.error('加载表列表失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const viewStructure = (table: DatabaseTable) => {
  selectedTable.value = table
  structureDialogVisible.value = true
}

const browseData = (table: DatabaseTable) => {
  // 跳转到查询页面并预填SQL
  const sql = `SELECT * FROM ${table.name} LIMIT 100;`
  router.push({
    path: '/query',
    query: { sql, connection: selectedConnection.value }
  })
}

onMounted(() => {
  loadConnections()
})
</script>

<style scoped>
.table-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}
</style>
