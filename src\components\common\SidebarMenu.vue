<template>
  <el-menu
    :default-active="activeMenu"
    :collapse="collapsed"
    :unique-opened="true"
    router
    class="sidebar-menu"
  >
    <el-menu-item index="/dashboard">
      <el-icon><Monitor /></el-icon>
      <template #title>仪表盘</template>
    </el-menu-item>

    <el-sub-menu index="connections">
      <template #title>
        <el-icon><Connection /></el-icon>
        <span>数据库连接</span>
      </template>
      <el-menu-item index="/connections">
        <el-icon><List /></el-icon>
        <template #title>连接管理</template>
      </el-menu-item>
      <el-menu-item index="/connections/new">
        <el-icon><Plus /></el-icon>
        <template #title>新建连接</template>
      </el-menu-item>
    </el-sub-menu>

    <el-sub-menu index="database">
      <template #title>
        <el-icon><Database /></el-icon>
        <span>数据库管理</span>
      </template>
      <el-menu-item index="/database/tables">
        <el-icon><Grid /></el-icon>
        <template #title>表管理</template>
      </el-menu-item>
      <el-menu-item index="/database/structure">
        <el-icon><Document /></el-icon>
        <template #title>结构查看</template>
      </el-menu-item>
    </el-sub-menu>

    <el-menu-item index="/query">
      <el-icon><Search /></el-icon>
      <template #title>SQL查询</template>
    </el-menu-item>

    <el-menu-item index="/history">
      <el-icon><Clock /></el-icon>
      <template #title>查询历史</template>
    </el-menu-item>

    <el-menu-item index="/settings">
      <el-icon><Setting /></el-icon>
      <template #title>系统设置</template>
    </el-menu-item>
  </el-menu>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import {
  Monitor,
  Connection,
  Database,
  Search,
  Clock,
  Setting,
  List,
  Plus,
  Grid,
  Document
} from '@element-plus/icons-vue'

interface Props {
  collapsed: boolean
}

defineProps<Props>()

const route = useRoute()

const activeMenu = computed(() => {
  return route.path
})
</script>

<style scoped>
.sidebar-menu {
  border: none;
  height: 100%;
}

.sidebar-menu:not(.el-menu--collapse) {
  width: 240px;
}
</style>
