<template>
  <div class="connection-form">
    <div class="page-header">
      <h2>{{ isEdit ? '编辑连接' : '新建连接' }}</h2>
      <el-button @click="$router.back()">返回</el-button>
    </div>

    <el-card>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        style="max-width: 600px"
      >
        <el-form-item label="连接名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入连接名称" />
        </el-form-item>

        <el-form-item label="数据库类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择数据库类型" style="width: 100%">
            <el-option label="MySQL" value="mysql" />
            <el-option label="PostgreSQL" value="postgresql" />
            <el-option label="SQLite" value="sqlite" />
            <el-option label="MongoDB" value="mongodb" />
            <el-option label="Redis" value="redis" />
          </el-select>
        </el-form-item>

        <el-form-item label="主机地址" prop="host">
          <el-input v-model="form.host" placeholder="请输入主机地址" />
        </el-form-item>

        <el-form-item label="端口" prop="port">
          <el-input-number 
            v-model="form.port" 
            :min="1" 
            :max="65535" 
            style="width: 100%" 
          />
        </el-form-item>

        <el-form-item label="用户名" prop="username">
          <el-input v-model="form.username" placeholder="请输入用户名" />
        </el-form-item>

        <el-form-item label="密码" prop="password">
          <el-input 
            v-model="form.password" 
            type="password" 
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>

        <el-form-item label="数据库名" prop="database">
          <el-input v-model="form.database" placeholder="请输入数据库名" />
        </el-form-item>

        <el-form-item label="SSL连接">
          <el-switch v-model="form.ssl" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="testConnection" :loading="testing">
            测试连接
          </el-button>
          <el-button type="success" @click="saveConnection" :loading="saving">
            保存连接
          </el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { databaseApi } from '@/api/database'
import type { DatabaseConnection } from '@/types'

const route = useRoute()
const router = useRouter()
const formRef = ref<FormInstance>()

const isEdit = computed(() => !!route.params.id)
const testing = ref(false)
const saving = ref(false)

const form = reactive({
  name: '',
  type: 'mysql' as DatabaseConnection['type'],
  host: 'localhost',
  port: 3306,
  username: '',
  password: '',
  database: '',
  ssl: false
})

const rules: FormRules = {
  name: [
    { required: true, message: '请输入连接名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择数据库类型', trigger: 'change' }
  ],
  host: [
    { required: true, message: '请输入主机地址', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入端口号', trigger: 'blur' }
  ],
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  database: [
    { required: true, message: '请输入数据库名', trigger: 'blur' }
  ]
}

const testConnection = async () => {
  if (!formRef.value) return
  
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return

  testing.value = true
  try {
    const success = await databaseApi.testConnection(form)
    if (success) {
      ElMessage.success('连接测试成功')
    } else {
      ElMessage.error('连接测试失败，请检查配置')
    }
  } catch (error) {
    ElMessage.error('连接测试失败')
    console.error(error)
  } finally {
    testing.value = false
  }
}

const saveConnection = async () => {
  if (!formRef.value) return
  
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return

  saving.value = true
  try {
    await databaseApi.createConnection(form)
    ElMessage.success('连接保存成功')
    router.push('/connections')
  } catch (error) {
    ElMessage.error('保存失败')
    console.error(error)
  } finally {
    saving.value = false
  }
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 根据数据库类型设置默认端口
const updateDefaultPort = () => {
  const portMap = {
    mysql: 3306,
    postgresql: 5432,
    sqlite: 0,
    mongodb: 27017,
    redis: 6379
  }
  form.port = portMap[form.type] || 3306
}

onMounted(() => {
  // 如果是编辑模式，加载连接数据
  if (isEdit.value) {
    // 这里应该根据ID加载连接数据
    // const connection = await databaseApi.getConnection(route.params.id)
    // Object.assign(form, connection)
  }
})
</script>

<style scoped>
.connection-form {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}
</style>
